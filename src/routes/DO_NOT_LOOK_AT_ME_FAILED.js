

const ProviderMusixmatch = (() => {
    const headers = {
        authority: "apic-desktop.musixmatch.com",
        cookie: "x-mxm-token-guid="
    };

    const baseURL = "https://apic-desktop.musixmatch.com/ws/1.1/";
    const appID = "web-desktop-app-v1.0";
    const subtitleFormat = "mxm";
    const userToken = CONFIG.providers.musixmatch.token;

    const createURL = (path, params) =>
        baseURL + path + "?" +
        Object.entries(params)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join("&");

    async function fetchFromAPI(path, params) {
        const url = createURL(path, { ...params, app_id: appID, subtitle_format: subtitleFormat, usertoken: userToken });
        const response = await tify.CosmosAsync.getSpice(url, null, headers);
        return response.message.body;
    }

    async function findLyrics(info) {
        const params = {
            q_album: info.album,
            q_artist: info.artist,
            q_artists: info.artist,
            q_track: info.title,
            track_spotify_id: info.uri,
            q_duration: info.duration / 1000,
            f_subtitle_length: Math.floor(info.duration / 1000)
        };

        const body = await fetchFromAPI("macro.subtitles.get", params);

        const track = body.macro_calls["matcher.track.get"].message.body.track;
        if (track.status_code !== 200) {
            return { error: `Requested error: ${track.mode}`, uri: info.uri };
        }

        if (body.macro_calls["track.lyrics.get"]?.message?.body?.lyrics?.restricted) {
            return { error: "Unfortunately we're not authorized to show these lyrics.", uri: info.uri };
        }

        return body;
    }

    async function getKaraoke(body) {
        const track = body.macro_calls["matcher.track.get"].message.body.track;
        if (!track.has_richsync || track.instrumental) return null;

        const params = {
            f_subtitle_length: track.track_length,
            q_duration: track.track_length,
            commontrack_id: track.commontrack_id
        };

        const result = await fetchFromAPI("track.richsync.get", params);
        if (result.header.status_code !== 200) return null;

        return JSON.parse(result.richsync.richsync_body).map(line => ({
            startTime: line.ts * 1000,
            text: line.l.map((word, index, words) => ({
                word: word.c,
                time: !isNaN(words[index + 1]?.o * 1000) ? words[index + 1].o * 1000 - word.o * 1000 : line.te * 1000 - (word.o * 1000 + line.ts * 1000)
            }))
        }));
    }

    function getSynced(body) {
        const track = body.macro_calls["matcher.track.get"].message.body.track;
        if (track.instrumental) return [{ text: "♪ Instrumental ♪", startTime: "0000" }];
        if (track.has_subtitles) {
            const subtitle = body.macro_calls["track.subtitles.get"].message.body.subtitle_list?.[0]?.subtitle;
            if (!subtitle) return null;
            return JSON.parse(subtitle.subtitle_body).map(line => ({
                text: line.text || "♪",
                startTime: line.time.total * 1000
            }));
        }
        return null;
    }

    function getUnsynced(body) {
        const track = body.macro_calls["matcher.track.get"].message.body.track;
        if (track.instrumental) return [{ text: "♪ Instrumental ♪" }];
        if (track.has_lyrics || track.has_lyrics_crowd) {
            const lyrics = body.macro_calls["track.lyrics.get"].message.body.lyrics?.lyrics_body;
            if (!lyrics) return null;
            return lyrics.split("\n").map(text => ({ text }));
        }
        return null;
    }

    async function getTranslation(body) {
        const track_id = body.macro_calls["matcher.track.get"].message.body.track.track_id;
        if (!track_id) return null;

        const result = await fetchFromAPI("crowd.track.translations.get", { track_id, translation_fields_set: "minimal", selected_language: "en" });
        if (!result.translations_list?.length) return null;

        return result.translations_list.map(({ translation }) => ({
            translation: translation.description,
            matchedLine: translation.matched_line
        }));
    }

    return { findLyrics, getKaraoke, getSynced, getUnsynced, getTranslation };
})();
