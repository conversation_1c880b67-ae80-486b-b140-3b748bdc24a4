import * as cheerio from 'cheerio';

export default async function extractLyrics(url) {
    // const res = await fetch(`https://genius.com/api/search/multi?q=${encodeURIComponent(query)}`);
    // const json = (await res.json());
    // const hits = json.response.sections[0].hits;
    // const hit = hits[0].result;
    // const lyricsUrl = await fetch("https://genius.com" + hit.path);
    const lyricsUrl = await fetch(url);
    const lyricsText = await lyricsUrl.text();
    const $ = cheerio.load(lyricsText);
    const lyricsHtml = $('div[data-lyrics-container|=true]');

    let lyrics = "";
    if (lyricsHtml.text()) {
        lyricsHtml.each((_, elem) => {
            lyrics += cheerio.load(cheerio.load(elem).html().replace(/<br>/gi, "\n")).text()
            lyrics += '\n'
        })
    }
    console.log(lyrics)
    return lyrics;
}

// const lyrics = await extractLyrics("https://genius.com/Coldplay-viva-la-vida-lyrics")
// console.log(lyrics)