export async function searchSpotify(query, types, market = 'US', limit = 20, offset = 0, includeExternal = 'audio') {
  const clientId = import.meta.env.VITE_CLIENTID;
  const clientSecret = import.meta.env.VITE_CLIENTSECRET;

  try {
      const tokenResponse = await fetch('https://accounts.spotify.com/api/token', {
          method: 'POST',
          body: new URLSearchParams({
              'grant_type': 'client_credentials',
              'client_id': clientId,
              'client_secret': clientSecret
          })
      });

      if (!tokenResponse.ok) {
          throw new Error(`Failed to obtain access token: ${tokenResponse.status} ${tokenResponse.statusText}`);
      }

      const tokenData = await tokenResponse.json();
      const accessToken = tokenData.access_token;

      const params = new URLSearchParams({
          q: query,
          type: types.join(','),
          market,
          limit,
          offset,
          include_external: includeExternal
      });

      const url = `https://api.spotify.com/v1/search?${params.toString()}`;

      const searchResponse = await fetch(url, {
          method: 'GET',
          headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
          }
      });

      if (!searchResponse.ok) {
          throw new Error(`Failed to fetch search results: ${searchResponse.status} ${searchResponse.statusText}`);
      }

      const searchData = await searchResponse.json();
      return searchData;
  } catch (error) {
      console.error('Error fetching data from Spotify API:', error);
      throw error;
  }
}

export async function getAlbumTracks(albumId, market = 'US', limit = 20, offset = 0) {
  const clientId = import.meta.env.VITE_CLIENTID;
  const clientSecret = import.meta.env.VITE_CLIENTSECRET;

  try {
      const tokenResponse = await fetch('https://accounts.spotify.com/api/token', {
          method: 'POST',
          body: new URLSearchParams({
              'grant_type': 'client_credentials',
              'client_id': clientId,
              'client_secret': clientSecret
          })
      });

      if (!tokenResponse.ok) {
          throw new Error(`Failed to obtain access token: ${tokenResponse.status} ${tokenResponse.statusText}`);
      }

      const tokenData = await tokenResponse.json();
      const accessToken = tokenData.access_token;

      const params = new URLSearchParams({
          market,
          limit,
          offset
      });

      const url = `https://api.spotify.com/v1/albums/${albumId}/tracks?${params.toString()}`;

      const albumTracksResponse = await fetch(url, {
          method: 'GET',
          headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
          }
      });

      if (!albumTracksResponse.ok) {
          throw new Error(`Failed to fetch album tracks: ${albumTracksResponse.status} ${albumTracksResponse.statusText}`);
      }

      const albumTracksData = await albumTracksResponse.json();
      return albumTracksData;
  } catch (error) {
      console.error('Error fetching album tracks from Spotify API:', error);
      throw error;
  }
}

// Example usage:
// const albumTracks = await getAlbumTracks('4aawyAB9vmqN3uQ7FjRGTy', 'US', 10, 0)
// console.log(albumTracks);
