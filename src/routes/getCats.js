export async function getCats(limit) {
  // limit is kinda weird, either 10 or 1 no in between
  if (limit>10 || limit<0) {
    console.log("Limit must be 10 or 1")
    return "https://http.cat/403.jpg"
  }
  if (limit==10) {
    const response = await fetch(`https://api.thecatapi.com/v1/images/search?limit=10`);
    const data = await response.json();
    return data
  } else {
    const response = await fetch(`https://api.thecatapi.com/v1/images/search`);
    const data = await response.json();
    return data
  }
}

const cats = await getCats(1)
console.log(cats[0].url)