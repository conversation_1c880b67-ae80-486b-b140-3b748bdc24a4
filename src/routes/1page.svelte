<script>
  import { Input } from "$lib/components/ui/input/index.js";
  import * as Select from "$lib/components/ui/select";
  import Sun from "svelte-radix/Sun.svelte";
  import <PERSON> from "svelte-radix/Moon.svelte";
  import { toggleMode } from "mode-watcher";
  import { But<PERSON> } from "$lib/components/ui/button/index.js";
  import { searchGenius } from "./geniusApi";
  import * as Card from "$lib/components/ui/card/index.js";
  import { searchSpotify } from "./searchSpotify.js";
  import { getAlbumTracks } from "./searchSpotify.js";
  import {
      buttonVariants
  } from "$lib/components/ui/button/index.js";
  import * as Dialog from "$lib/components/ui/dialog/index.js";
  import { Label } from "$lib/components/ui/label/index.js";
  import { Root } from "$lib/components/ui/drawer";
  import { getCats } from "./getCats";

  let songs = [];
  let searchPerformed = false;
  let catUrls = [];

  async function fetchCatImages(count) {
    const cats = await getCats(count);
    catUrls = cats.map(cat => cat.url);
  }

  async function handleSearch() {
      console.log("Search");
      console.log(searchMode);
      console.log(searchValue);

      let newSongs = [];
      if (searchMode === "Song") {
        newSongs = await searchGenius(searchValue);
    } else if (searchMode === "Album") {
        const spotifyData = await searchSpotify(searchValue, ['album'], 'US', 10, 0);
        for (const album of spotifyData['albums']['items']) {
            try {
                const albumtracks = await getAlbumTracks(album.id, 'US', 20, 0);
                newSongs.push({
                    type: album.type,
                    title: album.name,
                    artist: album.artists[0].name,
                    url: album.external_urls.spotify,
                    image: album.images[0].url,
                    release_date: album.release_date,
                    total_tracks: album.total_tracks,
                    album_tracks: albumtracks,
                    album_tracks_items: albumtracks.items
                });
                console.log(album);
            } catch (error) {
                console.error(`Error processing album ${album.name}:`, error);
            }
        }
    } else if (searchMode === "Artist") {
        const spotifyData = await searchSpotify(searchValue, ['artist'], 'US', 10, 0);
        console.log(spotifyData);
        for (const artist of spotifyData['artists']['items']) {
            console.log(artist);
            try {
                newSongs.push({
                    type: artist.type,
                    title: artist.name,
                    artist: artist.name,
                    image: artist.images[0]?.url || '', // handle missing image
                    url: artist.external_urls.spotify,
                    release_date: artist.id,
                    popularity: artist.popularity,
                    genres: artist.genres,
                    followers: artist.followers.total
                });
            } catch (error) {
                console.log(error);
            }
        }
    }

      songs = newSongs;
      searchPerformed = true;
      await fetchCatImages(10); // Fetch 3 cat images for error messages
  }

  let searchMode = "Select a Mode", searchValue;
</script>

<div class="flex flex-col items-center justify-center min-h-screen">
  <div class="flex items-center space-x-4 relative mb-8">
      <Button on:click={toggleMode} variant="outline" size="icon">
          <Sun class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span class="sr-only">Toggle theme</span>
      </Button>

      <div class="relative max-w-xs w-full">
          <Input bind:value={searchValue} type="text" placeholder={searchMode} class="pr-[110px] w-full" />
          <div class="absolute inset-y-0 right-0 flex items-center pr-2">
              <Select.Root>
                  <Select.Trigger class="w-[100px] h-[25px]">
                      <Select.Value placeholder="Mode" />
                  </Select.Trigger>
                  <Select.Content>
                      <Select.Item on:click={() => (searchMode = "Song")} value="Song">Song</Select.Item>
                      <Select.Item on:click={() => (searchMode = "Album")} value="Album">Album</Select.Item>
                      <Select.Item on:click={() => (searchMode = "Artist")} value="Artist">Artist</Select.Item>
                      <Select.Item on:click={() => (searchMode = "Custom")} value="Custom">Custom</Select.Item>
                  </Select.Content>
              </Select.Root>
          </div>
      </div>

      <Button variant="outline" class="ml-auto" on:click={handleSearch}>
          Search
      </Button>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      {#if searchPerformed}
          {#if songs.length > 0}
                {#if  searchMode === "Song"}
                    {#each songs as song}
                        <Card.Root class="w-[300px]">
                            <Card.Header>
                                <Card.Title>{song.title}</Card.Title>
                                <Card.Description>{song.artist}</Card.Description>
                            </Card.Header>
                            <Card.Content>
                                <img src={song.image} alt={song.title} class="w-full h-auto mb-4"/>
                                <p>Release Date: {song.release_date}</p>
                                <Dialog.Root>
                                    <Dialog.Trigger class={`${buttonVariants({ variant: "outline" })} w-[49%] h-full`}>View Lyrics</Dialog.Trigger>
                                    <Dialog.Content class="overflow-auto max-w-screen h-screen m-0.1">
                                        <Dialog.Header>
                                            <Dialog.Title>Lyrics for {song.title}</Dialog.Title>
                                        </Dialog.Header>
                                        {#if song.lyrics}
                                            <pre class="font-sans">{song.lyrics}</pre>
                                        {/if}
                                        {#if song.lyrics == null}
                                            <p>No lyrics found</p>
                                        {/if}
                                        <Dialog.Footer>
                                            <!-- Footer content, if any -->
                                        </Dialog.Footer>
                                    </Dialog.Content>
                                </Dialog.Root>
                                                                    
                            </Card.Content>
                        </Card.Root>
                    {/each}
                {:else if searchMode === "Album"}
                    {#each songs as song}
                        <Card.Root class="w-[300px]">
                            <Card.Header>
                                <Card.Title>{song.title}</Card.Title>
                                <Card.Description>{song.artist}</Card.Description>
                            </Card.Header>
                            <Card.Content>
                                <img src={song.image} alt={song.title} class="w-full h-auto mb-4"/>
                                <p>Release Date: {song.release_date}</p>
                                <Dialog.Root>
                                    <Dialog.Trigger class={`${buttonVariants({ variant: "outline" })} w-[49%] h-full`}>View Songs</Dialog.Trigger>
                                    <Dialog.Content class="overflow-auto max-w-screen h-screen m-0.1">
                                        <Dialog.Header>
                                            <Dialog.Title>Songs in album {song.title}</Dialog.Title>
                                        </Dialog.Header>
                                        {#if song.album_tracks_items}
                                            <div class="grid grid-cols-1 md:grid-cols-6 gap-1">
                                                {#each song.album_tracks_items as track}
                                                    <Card.Root class="w-[200px]">
                                                        <Card.Header>
                                                            <Card.Title>{track.track_number}. {track.name}</Card.Title>
                                                        </Card.Header>
                                                        <Card.Content>
                                                            <img src={song.image} alt={track.name} class="w-full h-auto mb-1"/>
                                                        </Card.Content>
                                                    </Card.Root>
                                                {/each}
                                            </div>
                                        {/if}
                                        {#if song.album_tracks_items == null}
                                            <p>No tracks found</p>
                                        {/if}
                                        <Dialog.Footer>
                                            <!-- Footer content, if any -->
                                        </Dialog.Footer>
                                    </Dialog.Content>
                                </Dialog.Root>
                                                                    
                            </Card.Content>
                        </Card.Root>
                    {/each}
                <!-- {:else if searchMode === "Artist"} -->
                <!-- {:else if searchMode === "Custom"} -->
                {/if}
          {:else}
              <!-- repeat this 3 times -->
              {#each catUrls as catUrl}
              <Card.Root class="w-[300px] flex flex-col items-center text-center">
                  <Card.Header>
                      <Card.Title>No Results Found</Card.Title>
                  </Card.Header>
                  <Card.Content>
                      <p>No results were found for your query, please try again. Good luck!</p>
                      <br>
                      <img src={catUrl} alt="cat" class="w-full h-auto mb-4">
                  </Card.Content>
              </Card.Root>
              {/each}
          {/if}
      {/if}
  </div>
</div>
