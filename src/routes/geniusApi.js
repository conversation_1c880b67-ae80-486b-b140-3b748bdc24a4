import extractLyrics from "./extractLyrics";

export async function searchGenius(query) {
    const url = `https://genius.com/api/search/multi?q=${encodeURIComponent(query)}`;
    
    try {
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.meta.status === 200) {
            const sections = data.response.sections;
            let results = [];
            
            for (const section of sections) {
                if (section.type === 'song') {
                    for (const hit of section.hits) {
                        var lyrics = await extractLyrics(hit.result.url);
                        console.log(results);
                        results.push({
                            type: hit.type,
                            title: hit.result.title,
                            artist: hit.result.primary_artist_names,
                            url: hit.result.url,
                            image: hit.result.song_art_image_url,
                            release_date: hit.result.release_date_for_display,
                            lyrics: lyrics
                        });
                    }
                }
            }
            return results;
        } else {
            throw new Error('Genius API request failed');
        }
    } catch (error) {
        console.error('Error fetching data from Genius API:', error);
        return null;
    }
}
// export async function searchGenius(query) {
//     const url = `https://genius.com/api/search/multi?q=${encodeURIComponent(query)}`;
    
//     try {
//         const response = await fetch(url);
//         const data = await response.json();
        
//         if (data.meta.status === 200) {
//             const sections = data.response.sections;
//             let results = [];
            
//             sections.forEach(section => {
//                 if (section.type === 'song') {
//                     section.hits.forEach(hit => {
//                         var lyrics = extractLyrics(hit.result.url);
//                         console.log(results);
//                         results.push({
//                             type: hit.type,
//                             title: hit.result.title,
//                             artist: hit.result.primary_artist_names,
//                             url: hit.result.url,
//                             image: hit.result.song_art_image_url,
//                             release_date: hit.result.release_date_for_display,
//                             lyrics: lyrics
//                         });
//                     });
//                  } //else if (section.type === 'lyric') {
//                 //     section.hits.forEach(hit => {
//                 //         results.push({
//                 //             type: hit.type,
//                 //             title: hit.result.title,
//                 //             artist: hit.result.primary_artist_names,
//                 //             url: hit.result.url,
//                 //             image: hit.result.header_image_thumbnail_url
//                 //         });
//                 //     });
//                 // }
//             });
            
//             return results;
//         } else {
//             throw new Error('Genius API request failed');
//         }
//     } catch (error) {
//         console.error('Error fetching data from Genius API:', error);
//         return null;
//     }
// }

// Example usage:
// const query = 'Viva La Vida';
// searchGenius(query).then(results => {
//     console.log(results);
// }).catch(error => {
//     console.error(error);
// });
