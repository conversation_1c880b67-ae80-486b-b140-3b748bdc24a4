{"name": "adcutalsveltlyricthing", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/eslint": "^8.56.7", "autoprefixer": "^10.4.19", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "postcss": "^8.4.38", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "prettier-plugin-tailwindcss": "^0.6.4", "svelte": "^4.2.7", "tailwindcss": "^3.4.4", "vite": "^5.0.3"}, "type": "module", "dependencies": {"@unovis/svelte": "^1.4.2", "bits-ui": "^0.21.12", "cheerio": "^1.0.0-rc.12", "clsx": "^2.1.1", "dotenv": "^16.4.5", "mode-watcher": "^0.3.1", "svelte-radix": "^1.1.0", "tailwind-merge": "^2.3.0", "tailwind-variants": "^0.2.1", "vaul-svelte": "^0.3.2"}}